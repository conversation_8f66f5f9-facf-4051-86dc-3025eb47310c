"""
集成测试专用配置
提供集成测试所需的fixtures和配置
"""

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from models.database import Base

# ============================================================================
# 集成测试专用fixtures
# ============================================================================


@pytest.fixture(scope="function")
def integration_db_session():
    """集成测试数据库会话"""
    # 使用独立的内存数据库
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    Base.metadata.create_all(engine)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)  # noqa: N806
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(engine)
        engine.dispose()


@pytest.fixture
def integration_test_data(integration_db_session):
    """集成测试数据准备"""
    # 在这里可以准备集成测试需要的基础数据
    yield integration_db_session


@pytest.fixture(scope="function")
def integration_db_session_factory():
    """集成测试数据库会话工厂"""
    # 创建会话工厂，用于需要控制会话生命周期的测试
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    Base.metadata.create_all(engine)
    TestingSessionFactory = sessionmaker(autocommit=False, autoflush=False, bind=engine)  # noqa: N806

    try:
        yield TestingSessionFactory
    finally:
        Base.metadata.drop_all(engine)
        engine.dispose()


# ============================================================================
# FastAPI测试客户端
# ============================================================================


@pytest.fixture
def shared_db_engine():
    """共享的测试数据库引擎"""
    from sqlalchemy import event

    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    # 启用SQLite外键约束
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

    # 删除并重新创建所有表
    Base.metadata.drop_all(engine)
    Base.metadata.create_all(engine)
    yield engine
    Base.metadata.drop_all(engine)
    engine.dispose()


@pytest.fixture
def client(shared_db_engine):
    """FastAPI测试客户端"""
    import os
    from fastapi import FastAPI
    from fastapi.testclient import TestClient

    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["RUN_MODE"] = "TEST"
    os.environ["MASTER_API_SECRET_KEY"] = "a_very_secret_key_for_development"

    # 创建简化的测试应用
    app = FastAPI(title="Test App", version="1.0.0")

    # 只包含规则详情相关的路由
    from api.routers.master.rule_details import rule_details_router

    app.include_router(rule_details_router)

    # 覆盖数据库依赖为测试数据库
    from core.db_session import get_db_session

    def override_get_db_session():
        # 使用共享的测试数据库引擎
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=shared_db_engine)  # noqa: N806
        session = TestingSessionLocal()
        try:
            yield session
        finally:
            session.close()

    app.dependency_overrides[get_db_session] = override_get_db_session

    # 添加统一错误处理
    try:
        from api.middleware.error_handling import UnifiedErrorHandlingMiddleware

        UnifiedErrorHandlingMiddleware.register_handlers(app)
    except ImportError:
        # 如果错误处理中间件不存在，跳过
        pass

    with TestClient(app) as test_client:
        yield test_client

    # 清理环境变量
    os.environ.pop("TESTING", None)
    os.environ.pop("RUN_MODE", None)
    os.environ.pop("MASTER_API_SECRET_KEY", None)


@pytest.fixture
def db_session(shared_db_engine):
    """数据库会话（与client fixture兼容）"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=shared_db_engine)  # noqa: N806
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()


# ============================================================================
# 集成测试配置
# ============================================================================


@pytest.fixture(autouse=True)
def integration_test_setup():
    """集成测试自动设置"""
    # 集成测试可能需要真实的组件交互
    # 但仍然使用测试数据库
    yield
