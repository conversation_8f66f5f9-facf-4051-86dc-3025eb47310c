#!/usr/bin/env python3
"""
字段元数据初始化集成测试
验证字段元数据初始化的完整流程，包括与数据库的实际交互、配置文件读取、数据一致性等
"""

import json
import subprocess
import sys
import tempfile
import time
from pathlib import Path

import pytest
from sqlalchemy.orm import Session

from models.database import (
    FieldTypeEnum,
    RuleFieldMetadata,
    RuleTemplate,
    RuleTemplateStatusEnum,
)
from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer
from tools.field_mapping_manager import FieldMappingManager


class TestRuleFieldMetadataIntegration:
    """字段元数据初始化集成测试类"""

    @pytest.fixture
    def sample_config_data(self):
        """示例配置数据"""
        return {
            "version": "3.1.0",
            "rule_type_mappings": {
                "integration_test_rule_1": {
                    "name": "集成测试规则1",
                    "required_fields": ["rule_name", "level1", "level2"],
                    "optional_fields": ["description", "reference"],
                },
                "integration_test_rule_2": {
                    "name": "集成测试规则2",
                    "required_fields": ["rule_name", "level1"],
                    "optional_fields": ["level3", "error_reason"],
                },
                "performance_test_rule": {
                    "name": "性能测试规则",
                    "required_fields": ["rule_name", "level1", "level2", "level3"],
                    "optional_fields": ["description", "reference", "error_reason", "degree"],
                },
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {
                        "chinese_name": "规则名称",
                        "data_type": "string",
                        "description": "规则的显示名称",
                        "excel_order": 1,
                    },
                    "level1": {
                        "chinese_name": "一级错误类型",
                        "data_type": "string",
                        "description": "一级错误类型分类",
                        "excel_order": 2,
                    },
                    "level2": {
                        "chinese_name": "二级错误类型",
                        "data_type": "string",
                        "description": "二级错误类型分类",
                        "excel_order": 3,
                    },
                    "level3": {
                        "chinese_name": "三级错误类型",
                        "data_type": "string",
                        "description": "三级错误类型分类",
                        "excel_order": 4,
                    },
                    "error_reason": {
                        "chinese_name": "错误原因",
                        "data_type": "text",
                        "description": "详细的错误原因说明",
                        "excel_order": 5,
                    },
                },
                "specific_fields": {
                    "description": {
                        "chinese_name": "描述",
                        "data_type": "text",
                        "description": "详细描述信息",
                        "excel_order": 26,
                    },
                    "reference": {
                        "chinese_name": "参考",
                        "data_type": "string",
                        "description": "参考信息",
                        "excel_order": 27,
                    },
                    "degree": {
                        "chinese_name": "严重程度",
                        "data_type": "string",
                        "description": "错误的严重程度",
                        "excel_order": 28,
                    },
                },
            },
        }

    @pytest.fixture
    def temp_config_file(self, sample_config_data):
        """临时配置文件"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
            json.dump(sample_config_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        yield temp_file

        # 清理
        Path(temp_file).unlink(missing_ok=True)

    @pytest.fixture
    def field_mapping_manager(self, temp_config_file):
        """字段映射管理器"""
        return FieldMappingManager(temp_config_file)

    @pytest.fixture
    def initializer(self, integration_db_session_factory, field_mapping_manager):
        """字段元数据初始化器"""
        return RuleFieldMetadataInitializer(integration_db_session_factory, field_mapping_manager)

    def test_complete_initialization_workflow(self, initializer, integration_db_session: Session):
        """测试完整的初始化工作流程"""
        # 清理现有数据
        integration_db_session.query(RuleFieldMetadata).delete()
        integration_db_session.query(RuleTemplate).delete()
        integration_db_session.commit()

        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")

        # 验证初始化结果
        assert result["total_rule_types"] == 3
        assert result["total_errors"] == 0
        assert result["created_templates"] == 3
        assert result["created_field_metadata"] > 0

        # 验证数据库中的数据
        templates = integration_db_session.query(RuleTemplate).all()
        metadata_records = integration_db_session.query(RuleFieldMetadata).all()

        assert len(templates) == 3
        assert len(metadata_records) > 0

        # 验证模板数据
        template_keys = {t.rule_key for t in templates}
        expected_keys = {"integration_test_rule_1", "integration_test_rule_2", "performance_test_rule"}
        assert template_keys == expected_keys

        # 验证字段元数据
        for template in templates:
            template_metadata = (
                integration_db_session.query(RuleFieldMetadata)
                .filter(RuleFieldMetadata.rule_key == template.rule_key)
                .all()
            )
            assert len(template_metadata) > 0

            # 验证必填字段存在
            field_names = {m.field_name for m in template_metadata}
            assert "rule_name" in field_names
            assert "level1" in field_names

    def test_incremental_update_mode(self, initializer, integration_db_session: Session):
        """测试增量更新模式"""
        # 先执行完整初始化
        result1 = initializer.initialize_all_metadata(mode="full")
        initial_templates = result1["created_templates"]
        initial_metadata = result1["created_field_metadata"]

        # 再执行增量更新
        result2 = initializer.initialize_all_metadata(mode="incremental")

        # 验证增量更新结果
        assert result2["total_rule_types"] == 3
        assert result2["total_errors"] == 0
        assert result2["updated_templates"] >= 0
        assert result2["updated_field_metadata"] >= 0

        # 验证数据库中的数据没有重复
        templates = integration_db_session.query(RuleTemplate).all()
        metadata_records = integration_db_session.query(RuleFieldMetadata).all()

        assert len(templates) == 3  # 应该还是3个模板

        # 验证没有重复的模板
        template_keys = [t.rule_key for t in templates]
        assert len(template_keys) == len(set(template_keys))

    def test_database_transaction_and_rollback(self, initializer, integration_db_session: Session):
        """测试数据库事务和回滚机制"""
        # 记录初始状态
        initial_template_count = integration_db_session.query(RuleTemplate).count()
        initial_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

        # 模拟初始化过程中的错误（通过修改配置）
        original_config = initializer.field_mapping_manager.config

        try:
            # 破坏配置以触发错误
            initializer.field_mapping_manager.config = {"invalid": "config"}

            # 尝试初始化，应该失败
            with pytest.raises(Exception):
                initializer.initialize_all_metadata(mode="full")

            # 验证数据库状态没有改变（事务回滚）
            final_template_count = integration_db_session.query(RuleTemplate).count()
            final_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

            assert final_template_count == initial_template_count
            assert final_metadata_count == initial_metadata_count

        finally:
            # 恢复原始配置
            initializer.field_mapping_manager.config = original_config

    def test_foreign_key_constraints(self, initializer, integration_db_session: Session):
        """测试外键约束验证"""
        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")
        assert result["total_errors"] == 0

        # 验证所有字段元数据都有对应的规则模板
        orphaned_metadata = (
            integration_db_session.query(RuleFieldMetadata)
            .filter(~RuleFieldMetadata.rule_key.in_(integration_db_session.query(RuleTemplate.rule_key)))
            .all()
        )

        assert len(orphaned_metadata) == 0, f"发现孤立的字段元数据记录: {[m.rule_key for m in orphaned_metadata]}"

        # 验证每个规则模板都有对应的字段元数据
        templates_without_metadata = (
            integration_db_session.query(RuleTemplate)
            .filter(~RuleTemplate.rule_key.in_(integration_db_session.query(RuleFieldMetadata.rule_key).distinct()))
            .all()
        )

        assert (
            len(templates_without_metadata) == 0
        ), f"发现没有字段元数据的模板: {[t.rule_key for t in templates_without_metadata]}"

    def test_excel_column_order_consistency(self, initializer, integration_db_session: Session):
        """测试Excel列顺序的一致性"""
        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")
        assert result["total_errors"] == 0

        # 验证Excel列顺序
        metadata_records = integration_db_session.query(RuleFieldMetadata).all()

        # 检查通用字段的顺序
        common_field_metadata = [m for m in metadata_records if m.is_fixed_field]
        for metadata in common_field_metadata:
            if metadata.field_name == "rule_name":
                assert metadata.excel_column_order == 1
            elif metadata.field_name == "level1":
                assert metadata.excel_column_order == 2
            elif metadata.field_name == "level2":
                assert metadata.excel_column_order == 3

        # 检查特定字段的顺序
        specific_field_metadata = [m for m in metadata_records if not m.is_fixed_field]
        for metadata in specific_field_metadata:
            assert metadata.excel_column_order >= 26, f"特定字段 {metadata.field_name} 的Excel顺序应该>=26"

    def test_data_consistency_validation(self, initializer, integration_db_session: Session):
        """测试数据一致性验证"""
        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")
        assert result["total_errors"] == 0

        # 验证字段类型映射正确
        metadata_records = integration_db_session.query(RuleFieldMetadata).all()

        for metadata in metadata_records:
            if metadata.field_name in ["rule_name", "level1", "level2", "level3", "reference"]:
                assert metadata.field_type == FieldTypeEnum.STRING
            elif metadata.field_name in ["description", "error_reason"]:
                assert metadata.field_type == FieldTypeEnum.STRING  # text映射为STRING

        # 验证必填字段标识正确
        rule1_metadata = (
            integration_db_session.query(RuleFieldMetadata)
            .filter(RuleFieldMetadata.rule_key == "integration_test_rule_1")
            .all()
        )

        required_fields = {"rule_name", "level1", "level2"}
        optional_fields = {"description", "reference"}

        for metadata in rule1_metadata:
            if metadata.field_name in required_fields:
                assert metadata.is_required == True, f"字段 {metadata.field_name} 应该是必填的"
            elif metadata.field_name in optional_fields:
                assert metadata.is_required == False, f"字段 {metadata.field_name} 应该是可选的"

    def test_configuration_change_impact(self, initializer, integration_db_session: Session, temp_config_file):
        """测试配置文件变更的影响"""
        # 执行初始初始化
        result1 = initializer.initialize_all_metadata(mode="full")
        initial_metadata_count = result1["created_field_metadata"]

        # 修改配置文件，添加新的规则类型
        with open(temp_config_file, "r", encoding="utf-8") as f:
            config = json.load(f)

        config["rule_type_mappings"]["new_test_rule"] = {
            "name": "新测试规则",
            "required_fields": ["rule_name", "level1"],
            "optional_fields": ["description"],
        }

        with open(temp_config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        # 重新加载配置
        initializer.field_mapping_manager = FieldMappingManager(temp_config_file)

        # 执行增量更新
        result2 = initializer.initialize_all_metadata(mode="incremental")

        # 验证新规则类型被添加
        assert result2["total_rule_types"] == 4  # 原来3个 + 新增1个

        new_template = (
            integration_db_session.query(RuleTemplate).filter(RuleTemplate.rule_key == "new_test_rule").first()
        )
        assert new_template is not None
        assert new_template.name == "新测试规则"

    def test_large_data_volume_performance(self, integration_db_session_factory, temp_config_file):
        """测试大数据量性能"""
        # 创建包含更多规则类型的配置
        with open(temp_config_file, "r", encoding="utf-8") as f:
            config = json.load(f)

        # 添加更多规则类型用于性能测试
        for i in range(10, 30):  # 添加20个规则类型
            config["rule_type_mappings"][f"perf_test_rule_{i}"] = {
                "name": f"性能测试规则{i}",
                "required_fields": ["rule_name", "level1", "level2"],
                "optional_fields": ["description", "reference", "error_reason"],
            }

        with open(temp_config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        # 创建新的初始化器
        field_mapping_manager = FieldMappingManager(temp_config_file)
        initializer = RuleFieldMetadataInitializer(integration_db_session_factory, field_mapping_manager)

        # 测量执行时间
        start_time = time.time()
        result = initializer.initialize_all_metadata(mode="full")
        end_time = time.time()

        execution_time = end_time - start_time

        # 验证性能要求（应该在合理时间内完成）
        assert execution_time < 30.0, f"初始化时间过长: {execution_time:.2f}秒"

        # 验证结果
        assert result["total_rule_types"] == 23  # 原来3个 + 新增20个
        assert result["total_errors"] == 0
        assert result["created_templates"] == 23
        assert result["created_field_metadata"] > 100  # 应该创建大量字段元数据

        print(
            f"性能测试结果: {result['total_rule_types']}个规则类型, "
            f"{result['created_field_metadata']}个字段元数据, "
            f"执行时间: {execution_time:.2f}秒"
        )

    def test_command_line_script_integration(self, temp_config_file, integration_db_session: Session):
        """测试命令行脚本的集成调用"""
        # 清理现有数据
        integration_db_session.query(RuleFieldMetadata).delete()
        integration_db_session.query(RuleTemplate).delete()
        integration_db_session.commit()

        # 构建命令行参数
        # 获取项目根目录（从tests/integration/services向上3级到项目根目录）
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "tools" / "initialize_field_metadata.py"

        # 检查脚本文件是否存在
        if not script_path.exists():
            pytest.skip(f"脚本文件不存在: {script_path}")
        cmd = [
            sys.executable,
            str(script_path),
            "--mode",
            "full",
            "--config",
            temp_config_file,
            "--force",  # 跳过确认提示
        ]

        # 执行命令行脚本
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, cwd=str(project_root))

            # 验证脚本执行成功
            assert result.returncode == 0, f"脚本执行失败: {result.stderr}"

            # 验证输出包含预期信息
            output = result.stdout
            assert "字段元数据初始化工具" in output
            assert "初始化成功完成" in output or "初始化完成" in output

            # 验证数据库中的数据
            templates = integration_db_session.query(RuleTemplate).all()
            metadata_records = integration_db_session.query(RuleFieldMetadata).all()

            assert len(templates) >= 3
            assert len(metadata_records) > 0

            print(f"命令行脚本测试成功: 创建了{len(templates)}个模板, {len(metadata_records)}个字段元数据")

        except subprocess.TimeoutExpired:
            pytest.fail("命令行脚本执行超时")
        except FileNotFoundError:
            pytest.skip("命令行脚本文件不存在，跳过测试")

    def test_dry_run_mode(self, temp_config_file, integration_db_session: Session):
        """测试试运行模式"""
        # 记录初始状态
        initial_template_count = integration_db_session.query(RuleTemplate).count()
        initial_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

        # 构建试运行命令
        # 获取项目根目录（从tests/integration/services向上3级到项目根目录）
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "tools" / "initialize_field_metadata.py"

        # 检查脚本文件是否存在
        if not script_path.exists():
            pytest.skip(f"脚本文件不存在: {script_path}")

        cmd = [sys.executable, str(script_path), "--mode", "full", "--config", temp_config_file, "--dry-run", "--force"]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, cwd=str(project_root))

            # 验证脚本执行成功
            assert result.returncode == 0, f"试运行脚本执行失败: {result.stderr}"

            # 验证输出包含试运行信息
            output = result.stdout
            assert "试运行模式" in output

            # 验证数据库状态没有改变
            final_template_count = integration_db_session.query(RuleTemplate).count()
            final_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

            assert final_template_count == initial_template_count
            assert final_metadata_count == initial_metadata_count

            print("试运行模式测试成功: 数据库状态未改变")

        except subprocess.TimeoutExpired:
            pytest.fail("试运行脚本执行超时")
        except FileNotFoundError:
            pytest.skip("命令行脚本文件不存在，跳过测试")

    def test_error_recovery_mechanism(self, initializer, integration_db_session: Session):
        """测试错误恢复机制"""
        # 执行正常初始化
        result1 = initializer.initialize_all_metadata(mode="full")
        assert result1["total_errors"] == 0

        # 记录正常状态
        normal_template_count = integration_db_session.query(RuleTemplate).count()
        normal_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

        # 模拟部分数据损坏（删除部分字段元数据）
        integration_db_session.query(RuleFieldMetadata).filter(
            RuleFieldMetadata.rule_key == "integration_test_rule_1"
        ).delete()
        integration_db_session.commit()

        # 验证数据确实被删除
        damaged_metadata_count = integration_db_session.query(RuleFieldMetadata).count()
        assert damaged_metadata_count < normal_metadata_count

        # 执行增量恢复
        result2 = initializer.initialize_all_metadata(mode="incremental")

        # 验证数据恢复
        recovered_template_count = integration_db_session.query(RuleTemplate).count()
        recovered_metadata_count = integration_db_session.query(RuleFieldMetadata).count()

        assert recovered_template_count == normal_template_count
        # 增量模式应该重新创建缺失的字段元数据
        assert recovered_metadata_count >= damaged_metadata_count

    def test_validation_engine_integration(self, initializer, integration_db_session: Session):
        """测试验证引擎集成"""
        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")
        assert result["total_errors"] == 0

        # 获取验证引擎
        if hasattr(initializer, "validation_engine") and initializer.validation_engine:
            validation_engine = initializer.validation_engine
        else:
            from services.rule_field_metadata_initializer import ValidationEngine

            validation_engine = ValidationEngine(initializer.field_mapping_manager, integration_db_session)

        # 生成验证报告
        validation_report = validation_engine.generate_validation_report()

        # 验证报告结构
        assert "overall_status" in validation_report
        assert "total_errors" in validation_report
        assert "total_warnings" in validation_report
        assert "validation_results" in validation_report

        # 验证通过所有检查
        assert validation_report["overall_status"] == "PASS"
        assert validation_report["total_errors"] == 0

        # 验证各项检查结果
        results = validation_report["validation_results"]
        assert results["config_completeness"]["status"] == "PASS"
        assert results["field_consistency"]["status"] == "PASS"
        assert results["foreign_key_integrity"]["status"] == "PASS"
        assert results["duplicate_detection"]["status"] == "PASS"

    def test_concurrent_initialization_safety(self, integration_db_session_factory, field_mapping_manager):
        """测试并发初始化的安全性（使用序列化方式避免SQLite并发问题）"""
        import queue
        import threading
        import time

        # 清理测试数据，确保每次测试从干净状态开始
        session = integration_db_session_factory()
        try:
            # 删除可能存在的测试数据
            session.query(RuleFieldMetadata).filter(RuleFieldMetadata.rule_key.like("concurrent_test_%")).delete(
                synchronize_session=False
            )
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("concurrent_test_%")).delete(
                synchronize_session=False
            )
            session.commit()
        finally:
            session.close()

        results_queue = queue.Queue()
        errors_queue = queue.Queue()

        # 使用锁来序列化数据库写入操作，避免SQLite并发问题
        db_lock = threading.Lock()

        def run_initialization(thread_id):
            try:
                # 创建带有线程ID和时间戳的唯一测试数据
                timestamp = int(time.time() * 1000)  # 毫秒时间戳
                unique_suffix = f"{thread_id}_{timestamp}"

                # 使用锁来序列化数据库操作
                with db_lock:
                    # 在独立的会话中创建测试数据
                    session = integration_db_session_factory()
                    try:
                        # 创建唯一的测试规则模板
                        test_template = RuleTemplate(
                            rule_key=f"concurrent_test_rule_{unique_suffix}",
                            rule_type=f"concurrent_test_type_{unique_suffix}",
                            name=f"并发测试规则{unique_suffix}",
                            description=f"线程{thread_id}的测试规则",
                            status=RuleTemplateStatusEnum.READY,
                        )
                        session.add(test_template)
                        session.commit()
                        session.refresh(test_template)
                    finally:
                        session.close()

                    # 使用初始化器处理数据（也需要序列化）
                    initializer = RuleFieldMetadataInitializer(integration_db_session_factory, field_mapping_manager)
                    result = initializer.initialize_all_metadata(mode="incremental")
                    results_queue.put((thread_id, result))
            except Exception as e:
                errors_queue.put((thread_id, str(e)))

        # 启动多个线程同时执行初始化
        threads = []
        for i in range(3):
            thread = threading.Thread(target=run_initialization, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=30)

        # 收集结果
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())

        errors = []
        while not errors_queue.empty():
            errors.append(errors_queue.get())

        # 验证至少有一个线程成功完成
        assert len(results) > 0, f"所有线程都失败了: {errors}"

        # 验证没有严重错误
        for _, result in results:
            # 每个线程处理自己的唯一数据，所以rule_types数量可能不同
            assert result["total_rule_types"] >= 0
            # 并发情况下可能有一些警告，但不应该有严重错误
            assert result["total_errors"] == 0 or result["total_errors"] < 5

        print(f"并发测试结果: {len(results)}个线程成功, {len(errors)}个线程失败")

        # 清理测试数据
        session = integration_db_session_factory()
        try:
            session.query(RuleFieldMetadata).filter(RuleFieldMetadata.rule_key.like("concurrent_test_%")).delete(
                synchronize_session=False
            )
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("concurrent_test_%")).delete(
                synchronize_session=False
            )
            session.commit()
        finally:
            session.close()
