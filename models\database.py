import enum
import json
from typing import Any, Optional

from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy import (
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class RuleStatusEnum(enum.Enum):
    NEW = "NEW"
    CHANGED = "CHANGED"
    READY = "READY"
    DEPRECATED = "DEPRECATED"


class MigrationStatusEnum(enum.Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

    # 旧的BaseRule模型已删除，使用新的RuleTemplate模型


# 旧的RuleDataSet模型已删除，使用新的RuleDetail模型
# 旧的RuleDetail类已删除，使用新的标准化模型


# ============================================================================
# 新的三表结构模型（使用标准字段名）
# ============================================================================


class RuleTemplateStatusEnum(enum.Enum):
    """规则模板状态枚举"""

    NEW = "NEW"
    CHANGED = "CHANGED"
    READY = "READY"
    DEPRECATED = "DEPRECATED"


class RuleDetailStatusEnum(enum.Enum):
    """规则明细状态枚举"""

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    DEPRECATED = "DEPRECATED"


class FieldTypeEnum(enum.Enum):
    """字段类型枚举"""

    STRING = "string"
    INTEGER = "integer"
    ARRAY = "array"
    BOOLEAN = "boolean"


class RuleTemplate(Base):
    """
    规则模板表（主表）
    存储规则模板的基本信息，作为其他表的外键引用源
    """

    __tablename__ = "rule_template"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    rule_key = Column(String(100), nullable=False, unique=True, comment="规则模板类型（业务主键）")
    rule_type = Column(String(100), nullable=False, comment="规则类型")
    name = Column(String(500), nullable=False, comment="规则模板名称")
    description = Column(Text, nullable=True, comment="规则模板描述")
    module_path = Column(String(500), nullable=True, comment="Python module path")
    file_hash = Column(String(64), nullable=True, comment="SHA-256 hash")
    status = Column(
        SQLAlchemyEnum(RuleTemplateStatusEnum),
        nullable=False,
        default=RuleTemplateStatusEnum.NEW,
        comment="规则模板状态",
    )
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关联关系
    rule_details = relationship("RuleDetail", back_populates="template", cascade="all, delete-orphan")
    field_metadata = relationship("RuleFieldMetadata", back_populates="template", cascade="all, delete-orphan")

    # 索引和约束
    __table_args__ = (
        Index("idx_rule_template_type", "rule_type"),
        Index("idx_rule_template_status", "status"),
    )

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "rule_key": self.rule_key,
            "rule_type": self.rule_type,
            "name": self.name,
            "description": self.description,
            "module_path": self.module_path,
            "file_hash": self.file_hash,
            "status": self.status.value if self.status else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "RuleTemplate":
        """从字典创建RuleTemplate对象"""
        # 处理状态枚举
        status = None
        if "status" in data and data["status"]:
            if isinstance(data["status"], str):
                status = RuleTemplateStatusEnum(data["status"])
            else:
                status = data["status"]

        # 创建对象，排除不需要的字段
        exclude_fields = {"id", "created_at", "updated_at"}
        filtered_data = {k: v for k, v in data.items() if k not in exclude_fields}

        # 设置状态
        if status:
            filtered_data["status"] = status

        return cls(**filtered_data)

    def get_field_metadata_list(self) -> list["RuleFieldMetadata"]:
        """获取关联的字段元数据列表"""
        return list(self.field_metadata)

    def get_field_metadata_by_name(self, field_name: str) -> Optional["RuleFieldMetadata"]:
        """根据字段名获取字段元数据"""
        for metadata in self.field_metadata:
            if metadata.field_name == field_name:
                return metadata
        return None

    def validate_template(self) -> dict[str, Any]:
        """验证模板配置"""
        errors = []
        warnings = []

        # 检查必填字段
        if not self.rule_key:
            errors.append("rule_key不能为空")
        if not self.name:
            errors.append("name不能为空")
        if not self.rule_type:
            errors.append("rule_type不能为空")

        # 检查字段元数据
        if not self.field_metadata:
            warnings.append("未配置字段元数据")

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}


class RuleDetail(Base):
    """
    规则明细表（子表）
    使用标准字段名，符合 field_mapping.json 配置
    通过rule_key外键关联到rule_template表
    """

    __tablename__ = "rule_detail"

    # 使用Integer而不是BigInteger，因为SQLite的autoincrement只对INTEGER类型有效
    # 在生产环境中，如果需要更大的ID范围，可以考虑使用BigInteger但需要手动管理ID生成
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    rule_id = Column(String(100), nullable=False, comment="规则ID")
    rule_key = Column(String(100), ForeignKey("rule_template.rule_key"), nullable=False, comment="规则模板类型")

    # 通用字段（使用标准命名）
    rule_name = Column(String(500), nullable=False, comment="规则名称")
    level1 = Column(String(255), nullable=False, comment="一级错误类型")
    level2 = Column(String(255), nullable=False, comment="二级错误类型")
    level3 = Column(String(255), nullable=False, comment="三级错误类型")
    error_reason = Column(Text, nullable=False, comment="错误原因")
    degree = Column(String(50), nullable=False, comment="错误程度")
    reference = Column(Text, nullable=False, comment="质控依据或参考资料")
    detail_position = Column(String(100), nullable=False, comment="具体位置描述")
    prompted_fields3 = Column(String(100), nullable=True, comment="提示字段类型")
    prompted_fields1 = Column(String(100), nullable=False, comment="提示字段编码")
    type = Column(String(100), nullable=False, comment="规则类别")
    pos = Column(String(100), nullable=False, comment="适用业务")
    applicableArea = Column(String(100), nullable=False, comment="适用地区")
    default_use = Column(String(50), nullable=False, comment="默认选用")
    remarks = Column(Text, nullable=True, comment="备注信息")
    in_illustration = Column(Text, nullable=True, comment="入参说明")
    start_date = Column(String(20), nullable=False, comment="开始日期")
    end_date = Column(String(20), nullable=False, comment="结束日期")

    # 固定的高频字段
    yb_code = Column(Text, nullable=True, comment="药品编码，逗号分隔")
    diag_whole_code = Column(Text, nullable=True, comment="完整诊断编码，逗号分隔")
    diag_code_prefix = Column(Text, nullable=True, comment="诊断编码前缀，逗号分隔")
    diag_name_keyword = Column(String(200), nullable=True, comment="诊断名称关键字，逗号分隔")
    fee_whole_code = Column(Text, nullable=True, comment="药品/诊疗项目完整编码，逗号分隔")
    fee_code_prefix = Column(Text, nullable=True, comment="药品/诊疗项目编码前缀，逗号分隔")

    # 扩展字段
    extended_fields = Column(Text, nullable=True, comment="JSON格式的扩展字段")

    # 元数据
    status = Column(
        SQLAlchemyEnum(RuleDetailStatusEnum),
        nullable=False,
        default=RuleDetailStatusEnum.ACTIVE,
        comment="记录状态",
    )
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关联关系
    template = relationship("RuleTemplate", back_populates="rule_details")

    # 索引
    __table_args__ = (
        Index("idx_rule_detail_rule_key", "rule_key"),
        Index("idx_rule_detail_rule_id", "rule_id"),
        Index("idx_rule_detail_status", "status"),
        Index("idx_rule_detail_key_status", "rule_key", "status"),
    )

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "rule_id": self.rule_id,
            "rule_key": self.rule_key,
            "rule_name": self.rule_name,
            "level1": self.level1,
            "level2": self.level2,
            "level3": self.level3,
            "error_reason": self.error_reason,
            "degree": self.degree,
            "reference": self.reference,
            "detail_position": self.detail_position,
            "prompted_fields3": self.prompted_fields3,
            "prompted_fields1": self.prompted_fields1,
            "type": self.type,
            "pos": self.pos,
            "applicableArea": self.applicableArea,
            "default_use": self.default_use,
            "remarks": self.remarks,
            "in_illustration": self.in_illustration,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "yb_code": self.yb_code,
            "diag_whole_code": self.diag_whole_code,
            "diag_code_prefix": self.diag_code_prefix,
            "diag_name_keyword": self.diag_name_keyword,
            "fee_whole_code": self.fee_whole_code,
            "fee_code_prefix": self.fee_code_prefix,
            "extended_fields": self.extended_fields,
            "status": self.status.value if self.status else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "RuleDetail":
        """从字典创建RuleDetail对象"""
        # 处理状态枚举
        status = None
        if "status" in data and data["status"]:
            if isinstance(data["status"], str):
                status = RuleDetailStatusEnum(data["status"])
            else:
                status = data["status"]

        # 创建对象，排除不需要的字段
        exclude_fields = {"id", "created_at", "updated_at"}
        filtered_data = {k: v for k, v in data.items() if k not in exclude_fields}

        # 设置状态
        if status:
            filtered_data["status"] = status

        return cls(**filtered_data)

    def get_extended_field(self, field_name: str, default: Any = None) -> Any:
        """获取扩展字段值"""
        if not self.extended_fields:
            return default

        try:
            extended_data = json.loads(self.extended_fields)
            return extended_data.get(field_name, default)
        except (json.JSONDecodeError, TypeError):
            return default

    def set_extended_field(self, field_name: str, value: Any) -> None:
        """设置扩展字段值"""
        try:
            if self.extended_fields:
                extended_data = json.loads(self.extended_fields)
            else:
                extended_data = {}

            extended_data[field_name] = value
            self.extended_fields = json.dumps(extended_data, ensure_ascii=False)
        except (json.JSONDecodeError, TypeError):
            # 如果解析失败，创建新的扩展字段
            extended_data = {field_name: value}
            self.extended_fields = json.dumps(extended_data, ensure_ascii=False)

    def get_all_extended_fields(self) -> dict[str, Any]:
        """获取所有扩展字段"""
        if not self.extended_fields:
            return {}

        try:
            return json.loads(self.extended_fields)
        except (json.JSONDecodeError, TypeError):
            return {}

    def update_extended_fields(self, fields: dict[str, Any]) -> None:
        """批量更新扩展字段"""
        try:
            if self.extended_fields:
                extended_data = json.loads(self.extended_fields)
            else:
                extended_data = {}

            extended_data.update(fields)
            self.extended_fields = json.dumps(extended_data, ensure_ascii=False)
        except (json.JSONDecodeError, TypeError):
            # 如果解析失败，直接使用新字段
            self.extended_fields = json.dumps(fields, ensure_ascii=False)

    def validate_data(self, field_metadata_list: list["RuleFieldMetadata"] | None = None) -> dict[str, Any]:
        """验证规则明细数据"""
        errors = []
        warnings = []

        # 基础字段验证
        if not self.rule_id:
            errors.append("rule_id不能为空")
        if not self.rule_key:
            errors.append("rule_key不能为空")
        if not self.rule_name:
            errors.append("rule_name不能为空")
        if not self.level1:
            errors.append("level1不能为空")
        if not self.level2:
            errors.append("level2不能为空")
        if not self.level3:
            errors.append("level3不能为空")
        if not self.error_reason:
            errors.append("error_reason不能为空")
        if not self.degree:
            errors.append("degree不能为空")
        if not self.reference:
            errors.append("reference不能为空")
        if not self.detail_position:
            errors.append("detail_position不能为空")
        if not self.prompted_fields1:
            errors.append("prompted_fields1不能为空")
        if not self.type:
            errors.append("type不能为空")
        if not self.pos:
            errors.append("pos不能为空")
        if not self.applicableArea:
            errors.append("applicableArea不能为空")
        if not self.default_use:
            errors.append("default_use不能为空")
        if not self.start_date:
            errors.append("start_date不能为空")
        if not self.end_date:
            errors.append("end_date不能为空")

        # 如果提供了字段元数据，进行详细验证
        if field_metadata_list:
            # 获取所有扩展字段
            extended_fields = self.get_all_extended_fields()

            # 验证每个字段
            for metadata in field_metadata_list:
                if metadata.is_fixed_field:
                    # 验证固定字段
                    field_value = getattr(self, metadata.field_name, None)
                else:
                    # 验证扩展字段
                    field_value = extended_fields.get(metadata.field_name)

                # 使用字段元数据验证
                validation_result = metadata.validate_field_value(field_value)
                if not validation_result["valid"]:
                    errors.extend(validation_result["errors"])
                warnings.extend(validation_result["warnings"])

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}

    def to_api_response(self) -> dict[str, Any]:
        """转换为API响应格式"""
        response_data = self.to_dict()

        # 解析扩展字段
        if self.extended_fields:
            try:
                extended_data = json.loads(self.extended_fields)
                response_data.update(extended_data)
            except (json.JSONDecodeError, TypeError):
                pass

        return response_data

    def merge_from_dict(
        self, data: dict[str, Any], field_metadata_list: list["RuleFieldMetadata"] | None = None
    ) -> None:
        """从字典合并数据到当前对象"""
        # 固定字段列表
        fixed_fields = {
            "rule_id",
            "rule_key",
            "rule_name",
            "level1",
            "level2",
            "level3",
            "error_reason",
            "degree",
            "reference",
            "detail_position",
            "prompted_fields3",
            "prompted_fields1",
            "type",
            "pos",
            "applicableArea",
            "default_use",
            "remarks",
            "in_illustration",
            "start_date",
            "end_date",
            "yb_code",
            "diag_whole_code",
            "diag_code_prefix",
            "diag_name_keyword",
            "fee_whole_code",
            "fee_code_prefix",
            "status",
        }

        # 分离固定字段和扩展字段
        fixed_data = {}
        extended_data = {}

        for key, value in data.items():
            if key in fixed_fields and hasattr(self, key):
                fixed_data[key] = value
            elif key not in {"id", "created_at", "updated_at"}:
                extended_data[key] = value

        # 更新固定字段
        for key, value in fixed_data.items():
            if key == "status" and isinstance(value, str):
                setattr(self, key, RuleDetailStatusEnum(value))
            else:
                setattr(self, key, value)

        # 更新扩展字段
        if extended_data:
            self.update_extended_fields(extended_data)


class RuleFieldMetadata(Base):
    """
    字段元数据表（子表）
    存储字段的元数据信息，支持动态Excel模板生成和数据校验
    通过rule_key外键关联到rule_template表
    """

    __tablename__ = "rule_field_metadata"

    id = Column(Integer, primary_key=True, index=True)
    rule_key = Column(String(100), ForeignKey("rule_template.rule_key"), nullable=False, comment="规则模板类型")
    field_name = Column(String(100), nullable=False, comment="字段名称")
    field_type = Column(SQLAlchemyEnum(FieldTypeEnum), nullable=False, comment="字段类型")
    is_required = Column(Boolean, nullable=False, default=False, comment="是否必填")
    is_fixed_field = Column(Boolean, nullable=False, default=False, comment="是否为固定字段")
    display_name = Column(String(200), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="字段描述")
    validation_rule = Column(Text, nullable=True, comment="JSON格式的校验规则")
    default_value = Column(Text, nullable=True, comment="默认值")
    excel_column_order = Column(Integer, nullable=True, comment="Excel列顺序")
    created_at = Column(DateTime, server_default=func.now())

    # 关联关系
    template = relationship("RuleTemplate", back_populates="field_metadata")

    # 索引和约束
    __table_args__ = (
        UniqueConstraint("rule_key", "field_name", name="uk_rule_field"),
        Index("idx_rule_field_metadata_rule_key", "rule_key"),
    )

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "rule_key": self.rule_key,
            "field_name": self.field_name,
            "field_type": self.field_type.value if self.field_type else None,
            "is_required": self.is_required,
            "is_fixed_field": self.is_fixed_field,
            "display_name": self.display_name,
            "description": self.description,
            "validation_rule": self.validation_rule,
            "default_value": self.default_value,
            "excel_column_order": self.excel_column_order,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "RuleFieldMetadata":
        """从字典创建RuleFieldMetadata对象"""
        # 处理字段类型枚举
        field_type = None
        if "field_type" in data and data["field_type"]:
            if isinstance(data["field_type"], str):
                field_type = FieldTypeEnum(data["field_type"])
            else:
                field_type = data["field_type"]

        # 创建对象，排除不需要的字段
        exclude_fields = {"id", "created_at"}
        filtered_data = {k: v for k, v in data.items() if k not in exclude_fields}

        # 设置字段类型
        if field_type:
            filtered_data["field_type"] = field_type

        return cls(**filtered_data)

    def get_validation_rules(self) -> list[str]:
        """获取解析后的验证规则列表"""
        if not self.validation_rule:
            return []

        try:
            rules = json.loads(self.validation_rule)
            if isinstance(rules, list):
                return rules
            elif isinstance(rules, str):
                return [rules]
            else:
                return []
        except (json.JSONDecodeError, TypeError):
            return []

    def validate_field_value(self, value: Any) -> dict[str, Any]:
        """验证字段值"""
        errors = []
        warnings = []

        # 检查必填字段
        if self.is_required and (value is None or value == ""):
            errors.append(f"{self.display_name}不能为空")

        # 检查字段类型
        if value is not None and value != "":
            if self.field_type == FieldTypeEnum.INTEGER:
                try:
                    int(value)
                except (ValueError, TypeError):
                    errors.append(f"{self.display_name}必须为整数")
            elif self.field_type == FieldTypeEnum.BOOLEAN:
                if not isinstance(value, bool) and str(value).lower() not in ["true", "false", "1", "0"]:
                    errors.append(f"{self.display_name}必须为布尔值")
            elif self.field_type == FieldTypeEnum.ARRAY:
                if not isinstance(value, list | tuple) and not isinstance(value, str):
                    errors.append(f"{self.display_name}必须为数组格式")

        # 应用验证规则
        validation_rules = self.get_validation_rules()
        for rule in validation_rules:
            if rule == "required" and (value is None or value == ""):
                errors.append(f"{self.display_name}不能为空")
            elif rule.startswith("max_length:"):
                try:
                    max_len = int(rule.split(":")[1])
                    if isinstance(value, str) and len(value) > max_len:
                        errors.append(f"{self.display_name}长度不能超过{max_len}个字符")
                except (ValueError, IndexError):
                    warnings.append(f"无效的验证规则: {rule}")
            elif rule.startswith("min:"):
                try:
                    min_val = float(rule.split(":")[1])
                    if isinstance(value, int | float) and value < min_val:
                        errors.append(f"{self.display_name}不能小于{min_val}")
                except (ValueError, IndexError):
                    warnings.append(f"无效的验证规则: {rule}")
            elif rule.startswith("max:"):
                try:
                    max_val = float(rule.split(":")[1])
                    if isinstance(value, int | float) and value > max_val:
                        errors.append(f"{self.display_name}不能大于{max_val}")
                except (ValueError, IndexError):
                    warnings.append(f"无效的验证规则: {rule}")

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}

    def get_default_value_parsed(self) -> Any:
        """获取解析后的默认值"""
        if not self.default_value:
            return None

        # 根据字段类型解析默认值
        try:
            if self.field_type == FieldTypeEnum.INTEGER:
                return int(self.default_value)
            elif self.field_type == FieldTypeEnum.BOOLEAN:
                return str(self.default_value).lower() in ["true", "1", "yes"]
            elif self.field_type == FieldTypeEnum.ARRAY:
                if self.default_value.startswith("[") and self.default_value.endswith("]"):
                    return json.loads(self.default_value)
                else:
                    return [self.default_value]
            else:
                return self.default_value
        except (ValueError, json.JSONDecodeError):
            return self.default_value
