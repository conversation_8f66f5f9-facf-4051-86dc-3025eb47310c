"""
规则明细管理API路由
使用统一的三表结构和标准字段名
"""

import logging

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import asc, desc, or_
from sqlalchemy.orm import Session

from api.dependencies.auth import get_api_key_dependency
from core.constants.error_codes import ErrorCodes
from core.db_session import get_db_session
from models.api import (
    ApiResponse,
    BatchOperationRequest,
    BatchOperationResponse,
    CreateRuleDetailRequest,
    PaginationResponse,
    RuleDetailResponse,
    UpdateRuleDetailRequest,
)
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate
from services.unified_data_mapping_engine import UnifiedDataMappingEngine

logger = logging.getLogger(__name__)

# 创建路由器
rule_details_router = APIRouter(
    prefix="/api/v1/rules/details",
    tags=["规则明细管理"],
    dependencies=[get_api_key_dependency()],
    responses={404: {"description": "Not found"}},
)

# 延迟初始化数据映射引擎
mapping_engine = None


def _get_mapping_engine():
    """获取数据映射引擎实例（延迟初始化）"""
    global mapping_engine
    if mapping_engine is None:
        mapping_engine = UnifiedDataMappingEngine()
    return mapping_engine


def _convert_rule_detail_to_response(rule_detail: RuleDetail) -> RuleDetailResponse:
    """将 RuleDetail 对象转换为响应格式"""
    return RuleDetailResponse(
        id=rule_detail.id,
        rule_id=rule_detail.rule_id,
        rule_key=rule_detail.rule_key,
        rule_name=rule_detail.rule_name,
        level1=rule_detail.level1,
        level2=rule_detail.level2,
        level3=rule_detail.level3,
        error_reason=rule_detail.error_reason,
        degree=rule_detail.degree,
        reference=rule_detail.reference,
        detail_position=rule_detail.detail_position,
        prompted_fields3=rule_detail.prompted_fields3,
        prompted_fields1=rule_detail.prompted_fields1,
        type=rule_detail.type,
        pos=rule_detail.pos,
        applicableArea=rule_detail.applicableArea,
        default_use=rule_detail.default_use,
        remarks=rule_detail.remarks,
        in_illustration=rule_detail.in_illustration,
        start_date=rule_detail.start_date,
        end_date=rule_detail.end_date,
        yb_code=rule_detail.yb_code,
        diag_whole_code=rule_detail.diag_whole_code,
        diag_code_prefix=rule_detail.diag_code_prefix,
        diag_name_keyword=rule_detail.diag_name_keyword,
        fee_whole_code=rule_detail.fee_whole_code,
        fee_code_prefix=rule_detail.fee_code_prefix,
        extended_fields=rule_detail.extended_fields,
        status=rule_detail.status.value if rule_detail.status else None,
        created_at=rule_detail.created_at.isoformat() if rule_detail.created_at else None,
        updated_at=rule_detail.updated_at.isoformat() if rule_detail.updated_at else None,
    )


@rule_details_router.post("/{rule_key}", response_model=ApiResponse[RuleDetailResponse])
def create_rule_detail(
    rule_key: str,
    request: CreateRuleDetailRequest,
    session: Session = Depends(get_db_session),
) -> ApiResponse[RuleDetailResponse]:
    """
    创建规则明细

    Args:
        rule_key: 规则键
        request: 创建请求
        session: 数据库会话

    Returns:
        ApiResponse[RuleDetailResponse]: 创建结果
    """
    try:
        logger.info(f"创建规则明细: rule_key={rule_key}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        # 2. 数据验证和标准化
        request_data = request.model_dump()
        validation_result = _get_mapping_engine().validate_data(request_data, rule_key)

        if not validation_result.valid:
            return ApiResponse.error_response(
                code=ErrorCodes.RULE_DETAIL_VALIDATION_FAILED,
                message=f"数据验证失败: {'; '.join(validation_result.errors)}",
            )

        # 3. 标准化字段名并处理数组字段
        normalized_data = _get_mapping_engine().normalize_field_names(request_data)

        # 4. 分离固定字段和扩展字段，同时处理数组字段转换
        fixed_fields, extended_fields = _get_mapping_engine().separate_fields(normalized_data)

        # 5. 如果有扩展字段，添加到 extended_fields 中
        if extended_fields:
            import json

            fixed_fields["extended_fields"] = json.dumps(extended_fields, ensure_ascii=False)

        # 6. 创建规则明细（使用处理后的固定字段，数组字段已转换为字符串）
        fixed_fields["rule_key"] = rule_key  # 确保包含 rule_key
        new_detail = RuleDetail(**fixed_fields)

        session.add(new_detail)
        session.commit()
        session.refresh(new_detail)

        logger.info(f"规则明细创建成功: id={new_detail.id}")

        return ApiResponse.success_response(
            data=_convert_rule_detail_to_response(new_detail), message="规则明细创建成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建规则明细失败: {e}", exc_info=True)
        session.rollback()
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"创建规则明细失败: {str(e)}"
            ).model_dump(),
        ) from None


@rule_details_router.get("/{rule_key}", response_model=ApiResponse[PaginationResponse])
def get_rule_details(
    rule_key: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: str | None = Query(None, description="状态过滤"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    search: str | None = Query(None, description="搜索关键词"),
    level1: str | None = Query(None, description="一级错误类型过滤"),
    level2: str | None = Query(None, description="二级错误类型过滤"),
    type: str | None = Query(None, description="规则类别过滤"),
    session: Session = Depends(get_db_session),
) -> ApiResponse[PaginationResponse]:
    """
    查询规则明细列表

    Args:
        rule_key: 规则键
        page: 页码
        page_size: 每页大小
        status: 状态过滤
        sort_by: 排序字段
        sort_order: 排序方向
        search: 搜索关键词
        level1: 一级错误类型过滤
        level2: 二级错误类型过滤
        type: 规则类别过滤
        session: 数据库会话

    Returns:
        ApiResponse[PaginationResponse]: 查询结果
    """
    try:
        logger.info(f"查询规则明细列表: rule_key={rule_key}, page={page}, page_size={page_size}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        # 2. 构建查询条件
        query = session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key)

        # 状态过滤
        if status:
            try:
                status_enum = RuleDetailStatusEnum(status)
                query = query.filter(RuleDetail.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=200,
                    detail=ApiResponse.error_response(
                        code=ErrorCodes.BAD_REQUEST, message=f"无效的状态值: {status}"
                    ).model_dump(),
                ) from None

        # 搜索过滤
        if search:
            search_filter = or_(
                RuleDetail.rule_name.contains(search),
                RuleDetail.rule_id.contains(search),
                RuleDetail.error_reason.contains(search),
            )
            query = query.filter(search_filter)

        # 字段过滤
        if level1:
            query = query.filter(RuleDetail.level1 == level1)
        if level2:
            query = query.filter(RuleDetail.level2 == level2)
        if type:
            query = query.filter(RuleDetail.type == type)

        # 3. 计算总数
        total = query.count()

        # 4. 排序
        if sort_order == "asc":
            query = query.order_by(asc(getattr(RuleDetail, sort_by, RuleDetail.created_at)))
        else:
            query = query.order_by(desc(getattr(RuleDetail, sort_by, RuleDetail.created_at)))

        # 5. 分页
        offset = (page - 1) * page_size
        items = query.offset(offset).limit(page_size).all()

        # 6. 转换响应格式
        data = [_convert_rule_detail_to_response(item) for item in items]

        pagination_response = PaginationResponse(
            items=data,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=(total + page_size - 1) // page_size,
        )

        logger.info(f"查询规则明细列表成功: 共{total}条记录，当前页{len(data)}条")

        return ApiResponse.success_response(data=pagination_response, message="查询成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询规则明细列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"查询规则明细列表失败: {str(e)}"
            ).model_dump(),
        ) from None


@rule_details_router.get("/{rule_key}/{detail_id}", response_model=ApiResponse[RuleDetailResponse])
def get_rule_detail(
    rule_key: str,
    detail_id: str,
    session: Session = Depends(get_db_session),
) -> ApiResponse[RuleDetailResponse]:
    """
    查询单条规则明细

    Args:
        rule_key: 规则键
        detail_id: 规则明细ID
        session: 数据库会话

    Returns:
        ApiResponse[RuleDetailResponse]: 查询结果
    """
    try:
        logger.info(f"查询单条规则明细: rule_key={rule_key}, detail_id={detail_id}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        # 2. 查询规则明细
        rule_detail = (
            session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == detail_id).first()
        )

        if not rule_detail:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_DETAIL_NOT_FOUND, message=f"规则明细 '{detail_id}' 不存在"
                ).model_dump(),
            )

        logger.info(f"查询单条规则明细成功: id={rule_detail.id}")

        return ApiResponse.success_response(data=_convert_rule_detail_to_response(rule_detail), message="查询成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询单条规则明细失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"查询单条规则明细失败: {str(e)}"
            ).model_dump(),
        ) from None


@rule_details_router.put("/{rule_key}/{detail_id}", response_model=ApiResponse[RuleDetailResponse])
def update_rule_detail(
    rule_key: str,
    detail_id: str,
    request: UpdateRuleDetailRequest,
    session: Session = Depends(get_db_session),
) -> ApiResponse[RuleDetailResponse]:
    """
    更新规则明细

    Args:
        rule_key: 规则键
        detail_id: 规则明细ID
        request: 更新请求
        session: 数据库会话

    Returns:
        ApiResponse[RuleDetailResponse]: 更新结果
    """
    try:
        logger.info(f"更新规则明细: rule_key={rule_key}, detail_id={detail_id}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        # 2. 查询规则明细
        rule_detail = (
            session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == detail_id).first()
        )

        if not rule_detail:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_DETAIL_NOT_FOUND, message=f"规则明细 '{detail_id}' 不存在"
                ).model_dump(),
            )

        # 3. 数据验证和标准化
        request_data = request.model_dump(exclude_unset=True)
        if request_data:  # 只有在有数据更新时才进行验证
            validation_result = _get_mapping_engine().validate_data(request_data, rule_key)

            if not validation_result.valid:
                return ApiResponse.error_response(
                    code=ErrorCodes.RULE_DETAIL_VALIDATION_FAILED,
                    message=f"数据验证失败: {'; '.join(validation_result.errors)}",
                )

            # 4. 标准化字段名并处理数组字段
            normalized_data = _get_mapping_engine().normalize_field_names(request_data)

            # 5. 分离固定字段和扩展字段，同时处理数组字段转换
            fixed_fields, extended_fields = _get_mapping_engine().separate_fields(normalized_data)

            # 6. 更新固定字段
            for field, value in fixed_fields.items():
                if hasattr(rule_detail, field) and value is not None:
                    setattr(rule_detail, field, value)

            # 7. 更新扩展字段
            if extended_fields:
                import json

                rule_detail.extended_fields = json.dumps(extended_fields, ensure_ascii=False)

        session.commit()
        session.refresh(rule_detail)

        logger.info(f"规则明细更新成功: id={rule_detail.id}")

        return ApiResponse.success_response(
            data=_convert_rule_detail_to_response(rule_detail), message="规则明细更新成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新规则明细失败: {e}", exc_info=True)
        session.rollback()
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"更新规则明细失败: {str(e)}"
            ).model_dump(),
        ) from None


@rule_details_router.delete("/{rule_key}/{detail_id}", response_model=ApiResponse[dict])
def delete_rule_detail(
    rule_key: str,
    detail_id: str,
    session: Session = Depends(get_db_session),
) -> ApiResponse[dict]:
    """
    删除规则明细

    Args:
        rule_key: 规则键
        detail_id: 规则明细ID
        session: 数据库会话

    Returns:
        ApiResponse[dict]: 删除结果
    """
    try:
        logger.info(f"删除规则明细: rule_key={rule_key}, detail_id={detail_id}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        # 2. 查询规则明细
        rule_detail = (
            session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == detail_id).first()
        )

        if not rule_detail:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_DETAIL_NOT_FOUND, message=f"规则明细 '{detail_id}' 不存在"
                ).model_dump(),
            )

        # 3. 删除规则明细
        session.delete(rule_detail)
        session.commit()

        logger.info(f"规则明细删除成功: id={rule_detail.id}")

        return ApiResponse.success_response(
            data={"deleted_id": detail_id, "rule_key": rule_key}, message="规则明细删除成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除规则明细失败: {e}", exc_info=True)
        session.rollback()
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"删除规则明细失败: {str(e)}"
            ).model_dump(),
        ) from None


@rule_details_router.post("/{rule_key}/batch", response_model=ApiResponse[BatchOperationResponse])
def batch_rule_details(
    rule_key: str,
    request: BatchOperationRequest,
    session: Session = Depends(get_db_session),
) -> ApiResponse[BatchOperationResponse]:
    """
    批量操作规则明细

    Args:
        rule_key: 规则键
        request: 批量操作请求
        session: 数据库会话

    Returns:
        ApiResponse[BatchOperationResponse]: 批量操作结果
    """
    try:
        logger.info(f"批量操作规则明细: rule_key={rule_key}, operation={request.operation}")

        # 1. 验证规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.RULE_NOT_FOUND, message=f"规则模板 '{rule_key}' 不存在"
                ).model_dump(),
            )

        success_count = 0
        failed_count = 0
        errors = []

        # 2. 根据操作类型执行批量操作
        if request.operation == "CREATE":
            for item_data in request.data:
                try:
                    # 数据验证和标准化
                    validation_result = _get_mapping_engine().validate_data(item_data, rule_key)
                    if not validation_result.valid:
                        failed_count += 1
                        errors.append(f"数据验证失败: {'; '.join(validation_result.errors)}")
                        continue

                    # 标准化字段名并处理数组字段
                    normalized_data = _get_mapping_engine().normalize_field_names(item_data)

                    # 分离固定字段和扩展字段，同时处理数组字段转换
                    fixed_fields, extended_fields = _get_mapping_engine().separate_fields(normalized_data)

                    # 如果有扩展字段，添加到 extended_fields 中
                    if extended_fields:
                        import json

                        fixed_fields["extended_fields"] = json.dumps(extended_fields, ensure_ascii=False)

                    # 创建规则明细（使用处理后的固定字段）
                    fixed_fields["rule_key"] = rule_key  # 确保包含 rule_key
                    new_detail = RuleDetail(**fixed_fields)
                    session.add(new_detail)
                    success_count += 1

                except Exception as e:
                    failed_count += 1
                    errors.append(f"创建失败: {str(e)}")

        elif request.operation == "UPDATE":
            for item_data in request.data:
                try:
                    detail_id = item_data.get("rule_id")
                    if not detail_id:
                        failed_count += 1
                        errors.append("缺少rule_id字段")
                        continue

                    # 查询规则明细
                    rule_detail = (
                        session.query(RuleDetail)
                        .filter(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == detail_id)
                        .first()
                    )

                    if not rule_detail:
                        failed_count += 1
                        errors.append(f"规则明细 '{detail_id}' 不存在")
                        continue

                    # 数据验证和标准化
                    update_data = {k: v for k, v in item_data.items() if k != "rule_id"}
                    if update_data:
                        validation_result = _get_mapping_engine().validate_data(update_data, rule_key)
                        if not validation_result["valid"]:
                            failed_count += 1
                            errors.append(f"数据验证失败: {'; '.join(validation_result['errors'])}")
                            continue

                        normalized_data = _get_mapping_engine().normalize_field_names(update_data)

                        # 更新字段
                        for field, value in normalized_data.items():
                            if hasattr(rule_detail, field) and value is not None:
                                setattr(rule_detail, field, value)

                    success_count += 1

                except Exception as e:
                    failed_count += 1
                    errors.append(f"更新失败: {str(e)}")

        elif request.operation == "DELETE":
            for item_data in request.data:
                try:
                    detail_id = item_data.get("rule_id")
                    if not detail_id:
                        failed_count += 1
                        errors.append("缺少rule_id字段")
                        continue

                    # 查询并删除规则明细
                    rule_detail = (
                        session.query(RuleDetail)
                        .filter(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == detail_id)
                        .first()
                    )

                    if not rule_detail:
                        failed_count += 1
                        errors.append(f"规则明细 '{detail_id}' 不存在")
                        continue

                    session.delete(rule_detail)
                    success_count += 1

                except Exception as e:
                    failed_count += 1
                    errors.append(f"删除失败: {str(e)}")

        else:
            raise HTTPException(
                status_code=200,
                detail=ApiResponse.error_response(
                    code=ErrorCodes.BAD_REQUEST, message=f"不支持的操作类型: {request.operation}"
                ).model_dump(),
            )

        # 3. 提交事务
        session.commit()

        logger.info(f"批量操作完成: 成功{success_count}条，失败{failed_count}条")

        response = BatchOperationResponse(
            success_count=success_count,
            failed_count=failed_count,
            errors=errors,
            total_count=success_count + failed_count,
        )

        return ApiResponse.success_response(data=response, message="批量操作完成")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量操作失败: {e}", exc_info=True)
        session.rollback()
        raise HTTPException(
            status_code=200,
            detail=ApiResponse.error_response(
                code=ErrorCodes.INTERNAL_ERROR, message=f"批量操作失败: {str(e)}"
            ).model_dump(),
        ) from None
